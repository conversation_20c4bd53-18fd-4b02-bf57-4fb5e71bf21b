import { Article } from '../../../types/news';
import './NewsComponent.css';

interface NewsComponentProps {
  id: number;
  article: Article | undefined;
}

const NewsComponent = (props: NewsComponentProps) => {
  // console.log(props);
  const { id, article } = props;
  return (
    <div className="flex flex-col items-center justify-center">
      <div id={String(id)}>
        <div>
          <p>{article?.title}</p>
        </div>
      </div>
    </div>
  );
};

export default NewsComponent;
