import './Home.css';
import { useToast } from '../../hooks/use-toast';
import { searchNews } from '../../services/news';
import { News, Article } from '../../types/news';
import { useState } from 'react';
import NewsComponent from '@/components/custom/newsComponent/NewsComponent';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

const Home = () => {
  const { toast } = useToast();
  const [query, setQuery] = useState<string>('');
  const [page, setPage] = useState<number>(1);
  //   const [news, setNews] = useState<News | null>(null);
  const [news, setNews] = useState<News | null>({
    status: 'ok',
    totalResults: 0,
    articles: [
      {
        source: {
          id: 'wired',
          name: 'Wired',
        },
        author: '<PERSON>',
        title: 'How Private Equity Killed the American Dream',
        description:
          'In her new book “Bad Company,” journalist <PERSON> chronicles how private equity upended industries from health care to local news—and the ways workers are fighting back.',
        url: 'https://www.wired.com/story/megan-greenwell-bad-company-private-equity-interview/',
        urlToImage:
          'https://media.wired.com/photos/68388fdbaeabdaf2c9e080ca/191:100/w_1280,c_limit/Megan-Greenwall-Q&A-Culture-**********.jpg',
        publishedAt: '2025-06-17T17:00:00Z',
        content:
          'In her new book, Bad Company: Private Equity and the Death of the American Dream, journalist and WIRED alum Megan Greenwell chronicles the devastating impacts of one of the most powerful yet poorly u… [+3586 chars]',
      },
      {
        source: {
          id: 'business-insider',
          name: 'Business Insider',
        },
        author: 'Alex Nicoll',
        title: 'The 14 dealmakers investors call when they want to offload their private equity fund stakes',
        description:
          "From Blackstone to Goldman Sachs: Meet 14 execs who helped lead private equity's secondaries market to a record $160 billion in deals last year.",
        url: 'https://www.businessinsider.com/private-equity-secondaries-power-players-blackstone-ares-goldman-sachs-clipway2025-5',
        urlToImage: 'https://i.insider.com/6839e5e7ca50259add4d5ef6?width=1200&format=jpeg',
        publishedAt: '2025-06-04T09:00:02Z',
        content:
          'Jeremy Coller, Coller Capital, Verdun Perry, Blackstone and Harold Hope, Goldman SachsCourtesy of Coller Capital, Blackstone , and Goldman Sachs\r\n<ul><li>Buying stakes in private funds can be lucrati… [+24743 chars]',
      },
      {
        source: {
          id: null,
          name: 'Gizmodo.com',
        },
        author: 'Lucas Ropek',
        title: 'Elon Musk’s xAI Is Reportedly Burning Through $1 Billion a Month',
        description: "Musk's new AI business is a cash-hungry monster.",
        url: 'https://gizmodo.com/elon-musks-xai-is-reportedly-burning-through-1-billion-a-month-2000617458',
        urlToImage: 'https://gizmodo.com/app/uploads/2025/04/elon-musk-feb-20-2025-1200x675.jpg',
        publishedAt: '2025-06-18T18:10:36Z',
        content:
          'Elon Musk has promised that his AI business, xAI, will help to revolutionize society. Before it can do that, however, the company is going to need to become fiscally viablea goal it hasn’t quite met … [+2835 chars]',
      },
      {
        source: {
          id: 'business-insider',
          name: 'Business Insider',
        },
        author: 'Ben Bergman,Rebecca Torrence',
        title: "General Catalyst's Hemant Taneja is trying to redefine venture capital — and baffling the industry",
        description:
          'General Catalyst is quietly laying the groundwork to become the first VC firm to go public. Its strategy is baffling the industry.',
        url: 'https://www.businessinsider.com/general-catalyst-hemant-taneja-redefine-venture-capital-baffling-industry-2025-5',
        urlToImage: 'https://i.insider.com/682221d33fe8d3928365936e?width=1200&format=jpeg',
        publishedAt: '2025-05-28T09:00:02Z',
        content:
          'Hemant Taneja, CEO of General CatalystSam Barnes/Sportsfile for Collision via Getty Images\r\nVenture firms are normally in the business of backing high-flying tech startups, so many in the industry we… [+10834 chars]',
      },
    ],
  });

  const handleSearch = async () => {
    console.log('Searching...');
    try {
      //   const t = await searchNews(query, page, 5);
      setPage(page + 1);
      //   setNews(t.data);
      toast({
        title: 'Search completed',
        description: 'We found 5 articles related to your keyword.',
        variant: 'success',
      });
    } catch (error) {
      console.log(error);
      toast({
        title: 'Error',
        description: 'Something went wrong. Please try again later.',
        variant: 'destructive',
      });
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="main-container">
      <h1>Home</h1>
      <div className="search-container">
        <input type="text" placeholder="Search..." onChange={e => setQuery(e.target.value)} onKeyDown={handleKeyDown} />
        <button onClick={handleSearch}>Search</button>
      </div>
      {news && news.articles.length > 0 ? (
        <div className="news-container">
          {news.articles.map((article: Article, index: number) => (
            <div key={index} className="news-item">
              <NewsComponent id={index} article={article} />
            </div>
          ))}
        </div>
      ) : (
        <div className="news-container">
          <h2>No news found</h2>
        </div>
      )}

      <Pagination>
  <PaginationContent>
    <PaginationItem>
      <PaginationPrevious href="#" />
    </PaginationItem>
    <PaginationItem>
      <PaginationLink href="#">1</PaginationLink>
    </PaginationItem>
    <PaginationItem>
      <PaginationEllipsis />
    </PaginationItem>
    <PaginationItem>
      <PaginationNext href="#" />
    </PaginationItem>
  </PaginationContent>
</Pagination>
    </div>
  );
};

export default Home;
