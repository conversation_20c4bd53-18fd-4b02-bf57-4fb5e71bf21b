.main-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: center; */
  height: 100vh;
  background-color: #f2f2f2;
  padding: 20px;
}

.search-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  gap: 10px;
}

.search-container input {
  width: 400px;
  height: 40px;
  border-radius: 5px;
  border: none;
  padding: 10px;
  font-size: 16px;
}

.search-container button {
  width: 100px;
  height: 40px;
  border-radius: 5px;
  background-color: #007bff;
  color: #fff;
  border: none;
  cursor: pointer;
}

.news-container {
  border: 1px solid #ccc;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 20px;
  height: 100%;
}

.news-item {
  margin: 20px;
  padding: 20px;
  border: 0.5px solid #ccc;
  border-radius: 5px;
}
