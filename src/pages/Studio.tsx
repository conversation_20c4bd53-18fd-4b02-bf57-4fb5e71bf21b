export default function Studio() {
    return (
      <div className="flex h-screen">
        {/* Left Sidebar */}
        <aside className="w-64 bg-gray-900 text-white p-4">
          <h2 className="text-lg font-semibold mb-4">📦 Components</h2>
          {/* Add draggable nodes here later */}
        </aside>
  
        {/* Canvas Center */}
        <main className="flex-1 bg-gray-100 p-4 overflow-hidden">
          <div className="w-full h-full border border-dashed border-gray-400 rounded-lg">
            {/* React Flow will go here */}
            <p className="text-center mt-20 text-gray-500">Canvas area</p>
          </div>
        </main>
  
        {/* Right Properties Panel */}
        <aside className="w-80 bg-white border-l border-gray-300 p-4">
          <h2 className="text-lg font-semibold mb-4">🛠️ Properties</h2>
          {/* Node details, prompt config, or chat logs */}
        </aside>
      </div>
    );
  }
  